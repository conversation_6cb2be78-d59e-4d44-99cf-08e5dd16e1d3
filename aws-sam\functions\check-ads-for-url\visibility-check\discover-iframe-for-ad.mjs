import { getElementId, logger } from '../utils/index.mjs';

/**
 * Discovers iframe for an ad based on its type and builds the CSS selector
 * @param {Object} page - Puppeteer page instance
 * @param {Object} ad - Ad object with id and type
 * @param {string} deviceName - Device name for logging
 * @param {boolean} checkExistenceOnly - If true, only check if container/iframe exists (for wrong-device detection)
 * @returns {Promise<Object>} Object with iframeHandle, parentContainerHandle, adSelector, iframeSrc, error
 */
export async function discoverIframeForAd(
  page,
  ad,
  deviceName,
  checkExistenceOnly = false
) {
  let iframeHandle = null;
  let parentContainerHandle = null;
  let parentContainerId = null;
  let iframeSrc = null;
  let adSelector = null;

  try {
    if (ad.type === 'watchbetter') {
      // --- Specific logic for watchbetter ---
      logger.silly(
        `### ➡️ [${deviceName}] ➡️ Checking ad ID: ${ad.id} (watchbetter). Searching for container: #${ad.id}`
      );
      const watchbetterContainerHandle = await page.$(`#${ad.id}`);
      if (watchbetterContainerHandle) {
        parentContainerId = await getElementId(watchbetterContainerHandle);
        iframeHandle = await watchbetterContainerHandle.waitForSelector(
          'iframe',
          { timeout: 5000, visible: true }
        );
        if (iframeHandle) {
          parentContainerHandle = watchbetterContainerHandle;
          // Extract iframe src attribute
          iframeSrc = await iframeHandle.evaluate((iframe) => iframe.src);
          // For watchbetter, the iframe has no ID. Build selector based on parent.
          adSelector = `#${parentContainerId} > iframe`;
          logger.silly(
            `### ➡️ [${deviceName}] ✅ Found iframe inside #${parentContainerId} for watchbetter ad. Selector: ${adSelector}`
          );
        }
      }
      console.timeEnd(`⏳ wait for watchbetter container`);
    } else {
      // --- Standard QMN ad logic ---
      const adContainerSelector = `#qmn${ad.id}`;
      console.log(
        `  [${deviceName}] 📦 Prüfe Ad Container: ${adContainerSelector}`
      );

      try {
        // Step 1: Wait for the ad container to be present on the page
        const containerHandle = await page.waitForSelector(
          adContainerSelector,
          { timeout: 10000 }
        );

        if (!containerHandle) {
          if (checkExistenceOnly) {
            return { containerFound: false, iframeFound: false };
          }
          return {
            error: 'Ad container element not found (timeout).',
          };
        }

        // Step 2: Look for an iframe *inside* that specific ad container
        iframeHandle = await containerHandle.waitForSelector(
          'iframe[id^="adspiritflash"]',
          { timeout: 10000 }
        );

        if (!iframeHandle) {
          await containerHandle.dispose();
          if (checkExistenceOnly) {
            return { containerFound: true, iframeFound: false };
          }
          return {
            error:
              'Iframe element not found within specific ad container (timeout).',
          };
        }

        // For existence check, return early with success
        if (checkExistenceOnly) {
          await iframeHandle.dispose();
          await containerHandle.dispose();
          return { containerFound: true, iframeFound: true };
        }

        // Extract iframe src and ID for QMN ads
        const iframeData = await iframeHandle.evaluate((iframe) => ({
          src: iframe.src,
          id: iframe.id,
        }));
        iframeSrc = iframeData.src;

        // Build adSelector from iframe ID
        if (!iframeData.id) {
          await containerHandle.dispose();
          await iframeHandle.dispose();
          return {
            error:
              'Iframe element found, but it has no ID, so it cannot be checked for visibility.',
          };
        }
        adSelector = `#${iframeData.id}`;
        logger.silly(
          `### ➡️ [${deviceName}] - Iframe ID found for Ad ID ${ad.id}: ${iframeData.id}`
        );

        // Set parent container for exclusion list
        parentContainerHandle = containerHandle;
        parentContainerId = await getElementId(containerHandle);
      } catch (error) {
        return {
          error: `Error during iframe discovery: ${error.message}`,
        };
      }
    }
  } catch (error) {
    logger.silly(
      `### ➡️ [${deviceName}] ❌ ERROR during iframe discovery for ad.id ${ad.id}: ${error.message}`
    );
    console.log(
      `### ➡️ [${deviceName}] [VISIBILITY] ❌ ERROR during iframe discovery for ad.id ${ad.id}: ${error.message}`
    );
    return {
      error: `Error during iframe discovery: ${error.message}`,
    };
  }

  if (!iframeHandle) {
    if (parentContainerHandle) await parentContainerHandle.dispose();
    return {
      error: 'Iframe element not found after hierarchical search.',
    };
  }

  return {
    iframeHandle,
    parentContainerHandle,
    adSelector,
    iframeSrc,
    error: null,
  };
}

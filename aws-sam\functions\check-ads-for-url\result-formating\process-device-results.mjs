import { processDeviceError } from './process-device-error.mjs';
import { processPlacementIssues } from './process-placement-issues.mjs';
import { processVisibilityIssues } from './process-visibility-issues.mjs';
import { processWrongDeviceIssues } from './process-wrong-device-issues.mjs';

/**
 * Processes device results and maps them to issues
 * @param {Array} deviceResults - Array of device result objects
 * @returns {Object} Object with issues array and iframeSources array
 */
export function processDeviceResults(deviceResults) {
  const issues = [];
  const allIframeSources = [];

  for (const result of deviceResults) {
    const responsiveKey = result.responsiveKey;

    // Collect iframe sources from this device result
    if (result.iframeSources && result.iframeSources.length > 0) {
      result.iframeSources.forEach((iframe) => {
        allIframeSources.push({
          ...iframe,
          deviceName: result.deviceName,
          responsiveKey: responsiveKey,
        });
      });
    }

    // Process device-level errors
    if (result.error) {
      processDeviceError(result, responsiveKey, issues);
    }

    // Process placement issues
    processPlacementIssues(result, responsiveKey, issues);

    // Process visibility issues
    processVisibilityIssues(result, responsiveKey, issues);

    // Process wrong-device issues
    processWrongDeviceIssues(result, responsiveKey, issues);
  }

  return {
    issues,
    iframeSources: allIframeSources,
  };
}

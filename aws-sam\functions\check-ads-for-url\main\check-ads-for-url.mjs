import { logger } from '../utils/logger.mjs';
import { formatMilliseconds } from '../utils/format-milliseconds.mjs';
import { runChecksForAllDevices } from './run-checks-for-all-devices.mjs';
import { extractAdsByResponsive } from '../result-formating/extract-ads-by-responsive.mjs';
import { processDeviceResults } from '../result-formating/index.mjs';
import { buildFinalResult } from '../result-formating/build-final-result.mjs';
import { calculateAdSummary } from '../result-formating/calculate-ad-summary.mjs';
import { calculateTimingBreakdown } from '../result-formating/calculate-timing-breakdown.mjs';
import { regroupQmnConfigByDevice } from '../result-formating/regroup-qmn-config.mjs';

export async function checkAdsForUrl(browser, params) {
  const { url, articleId, layers } = params;
  const sourceCommit = process.env.COMMIT_SHA || 'unknown';
  const activeLayers = layers || ['placement', 'visibility'];

  logger.info('Job started', { articleId: articleId });

  logger.silly(
    `📥 Start analysis: ${url} with layers [${activeLayers.join(', ')}]`
  );

  const startTime = performance.now();
  let durationMs = 0;

  try {
    // Run checks across all devices
    const deviceResults = await runChecksForAllDevices(
      browser,
      url,
      activeLayers
    );

    // Extract adsByResponsive from device results
    const adsByResponsive = extractAdsByResponsive(deviceResults);

    // Process device results and map to issues
    const { issues, iframeSources } = processDeviceResults(deviceResults);

    // Calculate ad summary by device and type
    const adSummary = calculateAdSummary(deviceResults, issues);

    // Calculate timing breakdown per device
    const timingBreakdown = calculateTimingBreakdown(deviceResults);

    // Extract and regroup qmnData from device results
    let qmnData = null;
    for (const result of deviceResults) {
      if (result.qmnData) {
        qmnData = result.qmnData;
        break;
      }
    }
    const qmnDataWithRegroupedAdSlots = qmnData
      ? regroupQmnConfigByDevice(qmnData)
      : null;

    // Calculate processing time
    const endTime = performance.now();
    durationMs = endTime - startTime;

    // Build final result
    const finalResult = buildFinalResult(
      issues,
      iframeSources,
      sourceCommit,
      adsByResponsive,
      durationMs,
      activeLayers,
      adSummary,
      timingBreakdown,
      qmnDataWithRegroupedAdSlots
    );

    logger.silly('final result 📈 :', JSON.stringify(finalResult, null, 2));
    await browser.close();

    return finalResult;
  } catch (e) {
    logger.error(
      `❌ Critical error during job execution for ${url}:`,
      e.message
    );
    return {
      success: true,
      sourceCommit,
      error: e.message,
      timestamp: new Date().toISOString(),
    };
  } finally {
    logger.info(`Job completed in ${formatMilliseconds(durationMs)}`);
  }
}

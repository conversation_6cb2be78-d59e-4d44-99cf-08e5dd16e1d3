import { logger } from '../utils/index.mjs';
import { runPlacementCheck } from '../placement-check/index.mjs';
import { runVisibilityCheck } from '../visibility-check/index.mjs';

/**
 * Runs placement and visibility checks for a set of ads
 * @param {Object} page - Puppeteer page instance
 * @param {Array} adsToCheck - Array of ads to check
 * @param {Array} adsOtherDevices - Array of ads that should NOT be rendered on this device
 * @param {string} deviceName - Name of the device being tested
 * @param {string} responsiveKey - Responsive type (desktop/mobile)
 * @param {Array} activeLayers - Array of active check layers (placement, visibility)
 * @returns {Promise<Object>} Object with issues, issueCount, and iframeSources
 */
export async function runAdChecks(
  page,
  adsToCheck,
  adsOtherDevices,
  deviceName,
  responsiveKey,
  activeLayers
) {
  const issues = {};
  let issueCount = 0;
  let iframeSources = [];

  if (adsToCheck.length > 0) {
    if (activeLayers.includes('placement')) {
      console.time(`⏳ run placement check`);
      issueCount += await runPlacementCheck(
        page,
        adsToCheck,
        deviceName,
        responsiveKey,
        issues
      );
      console.timeEnd(`⏳ run placement check`);
    }

    if (activeLayers.includes('visibility')) {
      console.time(`⏳ run visibility check`);
      const visibilityResult = await runVisibilityCheck(
        page,
        adsToCheck,
        adsOtherDevices,
        deviceName,
        responsiveKey,
        issues
      );
      issueCount += visibilityResult.notVisibleAdsCount || 0;
      issueCount += visibilityResult.wrongDeviceAdsCount || 0;
      iframeSources = visibilityResult.iframeSources || [];
      console.timeEnd(`⏳ run visibility check`);
    }
  } else {
    logger.silly(
      `[${deviceName}] No ads to check for device type '${responsiveKey}'.`
    );
    console.log(
      ` 🗂️[${deviceName}] [GROUP ADS BY RESPONSIVE] No ads to check for device type '${responsiveKey}'.`
    );
  }

  return {
    issues,
    issueCount,
    iframeSources,
  };
}
